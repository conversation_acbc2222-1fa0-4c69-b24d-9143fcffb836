package com.yhd.fa.marketing.cms.service.impl.logic.dataAuth;


import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yhd.common.util.LogUtils;
import com.yhd.fa.marketing.cms.dao.SaleMerchandiserRelationDAO;
import com.yhd.fa.marketing.cms.pojo.po.SaleMerchandiserRelationPO;
import com.yhd.fa.marketing.cms.util.SecurityUtil;
import org.slf4j.Logger;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version Id: DataAuthLogic.java, v 0.1 2025/4/30 15:00 JiangYuHong Exp $
 */
@Component
public class DataAuthLogic {

    private static final Logger logger = LogUtils.getLogger(DataAuthLogic.class.getName());

    @Resource
    private SaleMerchandiserRelationDAO saleMerchandiserRelationDAO;

    public List<String> getAllShowDataEmplCode(String account) {

        logger.info("get all show data empl code.");

        List<String> allShowDataEmplCode = new ArrayList<>();

        // 判断是否能查看全部数据
        if (SecurityUtil.ifShowAllData()) {

            return allShowDataEmplCode;

        } else if (SecurityUtil.ifCustomizeRole()) {

            allShowDataEmplCode.add(account);

            // 自定义角色
            Optional.ofNullable(SecurityUtil.getDataRoleBindEmployee())
                    .filter(CollUtil::isNotEmpty)
                    .ifPresent(allShowDataEmplCode::addAll);
            return allShowDataEmplCode;
        }

        allShowDataEmplCode.add(account);
        // 业务员
        LambdaQueryWrapper<SaleMerchandiserRelationPO> saleMerchandiserRelationPOLambdaQueryWrapper = new LambdaQueryWrapper<>();
        saleMerchandiserRelationPOLambdaQueryWrapper.eq(SaleMerchandiserRelationPO::getEmpNo, account);
        List<SaleMerchandiserRelationPO> saleMerchandiserRelationPOList = saleMerchandiserRelationDAO.selectList(saleMerchandiserRelationPOLambdaQueryWrapper);
        //判断该工号作为跟单员有没有跟业务
        Optional.ofNullable(saleMerchandiserRelationPOList)
                .filter(CollUtil::isNotEmpty)
                .map(saleMerchandiser ->
                        saleMerchandiser.stream().map(SaleMerchandiserRelationPO::getSalesNo).collect(Collectors.toList()))
                .ifPresent(allShowDataEmplCode::addAll);

        return allShowDataEmplCode;

    }
}
